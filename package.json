{"name": "vue-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start:dev": "cross-env NODE_ENV=dev node src/gmgnV3.cjs", "start:prod": "cross-env NODE_ENV=prod node src/gmgnV3.cjs", "dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.4.0", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "echarts": "^5.5.0", "element-plus": "^2.7.2", "node-sass": "^9.0.0", "puppeteer": "^24.9.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "sass-loader": "^13.3.2", "vue": "^3.4.21", "vue-echarts": "^6.7.3", "vue-router": "^4.3.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "sass": "^1.83.1", "vite": "^5.2.0"}}