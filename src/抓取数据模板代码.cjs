const puppeteer = require("puppeteer");
const axios = require("axios");

(async () => {
    const targetApiPrefix = "https://gmgn.ai/defi/quotation/v1/rank/sol/swaps/24h";

    const browser = await puppeteer.launch({
        headless: false, // 可见窗口
        defaultViewport: null
    });

    const page = await browser.newPage();

    await page.setRequestInterception(true);

    page.on("request", request => {
        const url = request.url();

        if (url.startsWith(targetApiPrefix)) {
            console.log("🌐 拦截到请求 URL：", url);

            // 修改 query 参数
            const newUrl = url
                .replace(/orderby=volume/, "orderby=marketcap")
                .replace(/direction=desc/, "direction=desc");

            console.log("🔁 重写后的 URL：", newUrl);

            // 继续发送修改后的请求，同时保留其他信息
            request.continue({ url: newUrl });
        } else {
            request.continue(); // 其它请求照常
        }
    });

    page.on("response", async response => {
        const url = response.url();
        if (url.startsWith(targetApiPrefix)) {
            try {
                const data = await response.json();
                console.log("📦 原始 API 响应：", JSON.stringify(data, null, 2));
            } catch (err) {
                console.error("❌ 解析响应失败：", err);
            }
        }
    });

    await page.goto("https://gmgn.ai/?chain=sol");
})();
