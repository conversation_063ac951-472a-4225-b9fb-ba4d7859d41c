// import {createApp} from 'vue'
// import './style.css'
// import App from './App.vue'
// import router from './router/index.js'
//
// import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'
//
// createApp(App).use(ElementPlus).use(router).mount('#app')


import {createApp} from 'vue'
import './style.css'
import App from './App.vue'
import router from './router/index.js'
import ElementPlus from 'element-plus'
// import ECharts from 'vue-echarts';
import * as Icons from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

const app=createApp(App)
app.use(ElementPlus,{
    locale: zhCn
})
app.use(router)
app.mount('#app')



Object.keys(Icons).forEach((key)=>{
    app.component(key,Icons[key])
})