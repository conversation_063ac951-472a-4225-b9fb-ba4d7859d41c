<template>
  <main class="main">
    <el-row>
      <!-- 左侧: 参数配置 -->
      <el-col :span="12" class="config-col">
        <div class="form">
          <el-form :model="form" label-width="120" style="max-width: 800px">
            <!-- symbol -->
            <el-form-item label="交易对：">
              <el-autocomplete
                  v-model="form.symbol"
                  :fetch-suggestions="querySearch"
                  placeholder="例如BTCUSDT这种全部大写"
                  style="width: 460px"
                  :trigger-on-focus="false"
              >
                <el-option v-for="item in suggestions" :key="item" :label="item" :value="item"/>
              </el-autocomplete>
            </el-form-item>

            <!-- interval -->
            <el-form-item label="K线周期：">
              <el-select v-model="form.interval" placeholder="请选择时间间隔" style="width: 460px">
                <el-option label="1分钟" value="ONE_MINUTE"/>
                <el-option label="3分钟" value="THREE_MINUTES"/>
                <el-option label="5分钟" value="FIVE_MINUTES"/>
                <el-option label="15分钟" value="FIFTEEN_MINUTES"/>
                <el-option label="30分钟" value="THIRTY_MINUTES"/>
                <el-option label="1小时" value="ONE_HOUR"/>
                <el-option label="2小时" value="TWO_HOURS"/>
                <el-option label="4小时" value="FOUR_HOURS"/>
                <el-option label="6小时" value="SIX_HOURS"/>
                <el-option label="8小时" value="EIGHT_HOURS"/>
                <el-option label="12小时" value="TWELVE_HOURS"/>
                <el-option label="1天" value="ONE_DAY"/>
                <el-option label="3天" value="THREE_DAYS"/>
                <el-option label="1周" value="ONE_WEEK"/>
                <el-option label="1个月" value="ONE_MONTH"/>
              </el-select>
            </el-form-item>

            <!-- klineWindow -->
            <el-form-item label="K线窗口大小：">
              <el-input-number v-model="form.klineWindow" :precision="0" :min="1" :max="100" style="width: 460px"/>
            </el-form-item>

            <!-- takeProfit -->
            <el-form-item label="止盈百分点：">
              <el-input-number v-model="form.takeProfit" :precision="2" :min="0" :max="100" style="width: 460px"/>
            </el-form-item>

            <!-- stopLossProfit -->
            <el-form-item label="止损百分点：">
              <el-input-number v-model="form.stopLossProfit" :precision="2" :min="0" :max="100" style="width: 460px"/>
            </el-form-item>

            <!-- tradeMode -->
            <el-form-item label="交易模式：">
              <el-select v-model="form.tradeMode" placeholder="请选择交易模式" style="width: 460px">
                <el-option label="历史" value="HISTORY"/>
                <el-option label="实盘" value="NOW"/>
              </el-select>
            </el-form-item>

            <!-- klineStrategy -->
            <el-form-item label="交易策略：">
              <el-select v-model="form.klineStrategy" placeholder="请选择交易策略" style="width: 460px">
                <el-option label="布尔中轨策略" value="BOLL_MIDDLE"/>
                <el-option label="十字星策略" value="CROSS"/>
              </el-select>
            </el-form-item>

            <!-- accountInitialBalance -->
            <el-form-item label="账户初始余额：">
              <el-input-number v-model="form.accountInitialBalance" :precision="2" :min="0" :max="*********"
                               style="width: 460px"/>
            </el-form-item>

            <!-- position -->
            <el-form-item label="单次交易仓位：">
              <el-input-number v-model="form.position" :precision="2" :min="0" :max="*********" style="width: 460px"/>
            </el-form-item>

            <!-- startTime -->
            <el-form-item label="策略开始时间：">
              <el-date-picker v-model="form.startTime" type="datetime" placeholder="选择开始时间" style="width: 460px"/>
            </el-form-item>

            <!-- endTime -->
            <el-form-item label="策略结束时间：">
              <el-date-picker v-model="form.endTime" type="datetime" placeholder="选择结束时间" style="width: 460px"/>
            </el-form-item>

            <!-- 保存按钮 -->
            <el-form-item>
              <el-button type="primary" @click="go" v-text="loading ? '启动中' : '启动'" :disabled="loading"
                         style="margin-top: 20px; width: 100px;"></el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-col>

      <!-- 右侧: WebSocket 数据展示 -->
      <el-col :span="12" class="ws-col">
        <div class="progress">
          <!-- 历史数据 -->
          <el-progress
              v-if="progressObj?.type === 'HTTP_PROGRESS_BAR'"
              :text-inside="true"
              :stroke-width="20"
              :percentage="progressObj.data.c">
            <span>{{ progressObj?.data?.c }}%</span>
          </el-progress>
          <!-- 实时数据 -->
          <el-progress
              v-else
              :percentage="80"
              :format="format"
              :indeterminate="true"
              :stroke-width="20"
          />
        </div>

        <div v-if="messages && messages.length > 0" class="content">
          <el-card v-for="(message, index) in messages" :key="index" class="box-card" style="margin-bottom: 20px">
            <div slot="header" class="clearfix">
              <span><strong>时间:</strong> {{ message.time }}</span>
            </div>
            <div>
              <el-col>
                <p><strong>触发策略:</strong> {{ message.data.klineStrategyEnum }}</p>
              </el-col>
              <el-col>
                <p><strong>当前余额:</strong> {{ message.data.currentBalance }}</p>
              </el-col>
              <el-col :span="12">
                <p><strong>操作金额:</strong> {{ message.data.opAmount }}</p>
              </el-col>
              <el-col :span="12">
                <p><strong>操作价格:</strong> {{ message.data.opPrice }}</p>
              </el-col>
              <el-col :span="12">
                <p><strong>操作类型:</strong> {{ message.data.opType }}</p>
              </el-col>
              <el-col :span="12">
                <p><strong>订单ID:</strong> {{ message.data.orderId }}</p>
              </el-col>
              <el-col :span="12">
                <p><strong>订单状态:</strong> {{ message.data.orderStatus }}</p>
              </el-col>
              <el-col :span="12">
                <p><strong>K线日期:</strong> {{ message.data.klineDate }}</p>
              </el-col>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </main>
</template>

<script setup>
import {reactive, ref, unref, onMounted} from 'vue'
import {$updateConfig} from '../../api/ExchangesBotApi.ts'
import {ElMessage} from 'element-plus'
import {useWebSocket} from '../../utils/webSocket.js'
import {baseHost} from "../../utils/request.js"

const {connect, messages, error, progressObj} = useWebSocket(`ws://${baseHost}/v1/exchangeBot`)


const suggestions = ref(['BTCUSDT', 'ETHUSDT', 'SOLUSDT'])
const querySearch = (queryString, cb) => {
  cb(suggestions.value)
}

const loading = ref(false)
const form = reactive({});// 表单数据

const format = (val) => {
  const type = unref(progressObj)?.type
  return type ? type === 'HTTP_PROGRESS_BAR' ? '' : unref(progressObj)?.data?.status : '请先启动'
}

const go = async () => {
  try {
    localStorage.setItem('paramsConfigForm', JSON.stringify(form))
    loading.value = true
    // messages.value = []
    // 发送配置数据
    messages.value.splice(0)
    const res = await $updateConfig(form)
    if (res?.success) {
      ElMessage({message: res.message, type: 'success'})
      loading.value = false
    }
  } catch (error) {
    ElMessage({message: error, type: 'error'})
    loading.value = false
  }
}

onMounted(() => {
  let initForm = JSON.parse(localStorage.getItem('paramsConfigForm')) || {
    symbol: 'BTCUSDT',
    interval: 'FOUR_HOURS',
    klineWindow: 20,
    takeProfit: 5,
    stopLossProfit: 5,
    tradeMode: 'HISTORY',
    klineStrategy: 'BOLL_MIDDLE',
    accountInitialBalance: 10000,
    position: 10000,
    startTime: '2025-01-01 00:00:00',
    endTime: '2025-01-09 00:00:00',
  }
  Object.assign(form, initForm)
})

</script>

<style scoped lang="scss">
.main {
  display: flex;
  flex-direction: row;
  padding: 20px;
}

.config-col {
  padding: 10px;
  position: sticky;
  top: 0; /* 固定在顶部 */
  height: 100vh; /* 确保左侧区域始终固定 */
  overflow-y: auto; /* 如果内容超过，滚动条将会出现 */
}

.container {
  display: flex;
  height: 100%;
  width: 100%;
}

.left-side {
  flex: 0 0 460px; /* 固定左边表单的宽度 */
  padding: 20px;
  overflow-y: auto; /* 如果左边内容溢出，可以滚动 */
}

.right-side {
  flex: 1; /* 右边部分占满剩余空间 */
  padding: 20px;
  overflow-y: auto; /* 右边的内容可以滚动 */
  max-height: 100%;
}

.ws-col {
  padding: 10px;
}

.progress {
  position: sticky;
  top: 0; /* 进度条保持在顶部 */
  z-index: 10;
  background-color: white;
  padding: 10px;
  box-shadow: 0 4px 2px -2px gray;
}

.content {
  overflow-y: auto; /* 内容区域可以滚动 */
  max-height: calc(100vh - 90px); /* 限制内容部分的最大高度 */
}

.box-card {
  margin-bottom: 20px;
}
</style>
