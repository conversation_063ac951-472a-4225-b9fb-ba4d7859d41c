<template>
    <div class="large-holdings-detail-container">
      <div class="canvas" ref="myRef"></div>
    </div>
  </template>
   
<script setup>
import * as echarts from 'echarts'
import { ref, onMounted} from 'vue'

const myRef = ref(null) // 获取dom实例
const options=ref({})
const getData=async()=>{
    options.value={
        title: {
            text: '内测功能'
        },
        xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: [{
            type: 'value',
            // axisLabel:{
            //    // 函数模板
            //     formatter:function(value,index){
            //         let result=''
            //         if(value<=98 && value>96){
            //             result='96'
            //         }else if(value<=100 && value>98){
            //             result='98'
            //         }else if(value<=102 && value>100){
            //             result='100'
            //         }else{
            //             result='90'
            //         }
            //         return result
            //     }
            // }
        }],
        series: [
            {
                data: [10, 101, 102, 99, 98, 97, 91.5],
                type: 'line'
            }
        ]
    }
    
}
const renderChart = () => {
    const myChart = echarts.init(myRef.value)
    myChart.setOption(options.value)
}
onMounted(() => {
    getData()
    renderChart() // 生命周期挂载函数渲染图表
})
</script>
   
<style lang="scss" scoped>
.large-holdings-detail-container{
    width:100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .canvas {
        width: 90%;
        height: 90%;
    }
}

</style>