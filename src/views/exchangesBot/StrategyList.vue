<template>
  <div class="large-holdings-container">

    <!--        <div class="large-holdings-header">-->
    <!--            <el-form :model="holdForm" class="hold-form" label-width="100">-->

    <!--                <el-form-item label="货币名称" class="form-item">-->
    <!--                    <el-select-->
    <!--                    v-model="holdForm.currency"-->
    <!--                    placeholder="请选择"-->
    <!--                    clearable>-->
    <!--                     <el-option-->
    <!--                     v-for="item in currencyList"-->
    <!--                     :label="item.label"-->
    <!--                     :value="item.value"-->
    <!--                     :key="item.value"-->
    <!--                     />-->
    <!--                    </el-select>-->
    <!--                </el-form-item>-->

    <!--                <el-form-item class="form-item reset-query" >-->
    <!--                    <el-button>清空</el-button>-->
    <!--                    <el-button type="primary">查询</el-button>-->
    <!--                </el-form-item>-->
    <!--            </el-form>-->
    <!--        </div>-->

    <div class="large-holdings-content">
      <div class="large-holdings-table">
        <el-table :data="tableData" stripe border v-loading="loading">
          <el-table-column
              v-for="(item,index) in columns"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              show-overflow-tooltip
              :width="item.width || 'auto'">
            <template #default="{ row }">
              <span v-if="item.prop === 'yield' && typeof row[item.prop] === 'number'">
                {{
                  (row[item.prop]).toFixed(2)
                }}%</span>
              <span v-else>{{ row[item.prop] }}</span>
            </template>
            <template #header>
              <span :title="item.label">{{ item.label }}</span>
            </template>
          </el-table-column>
          <!--          <el-table-column align="center" fixed="right" label="操作" show-overflow-tooltip width="220">-->
          <!--            <template #default="{ row }">-->
          <!--              <div class="rowOptions">-->
          <!--                <el-button text type="primary" @click="viewHistory(row)">-->
          <!--                  查看历史-->
          <!--                </el-button>-->
          <!--              </div>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
        </el-table>
      </div>

      <div class="pagination">
        <el-pagination
            v-model:current-page="pagination.pageNo"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            :small="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>

  </div>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {columns} from './column.js'
import {$getLargeHoldingsPageData} from '../../api/ExchangesBotApi.ts'
import {useRouter} from 'vue-router';

const router = useRouter()

const holdForm = reactive({
  currency: ''
})
const tableData = ref([])
const total = ref(0)
const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})
const loading = ref(false)


//获取列表数据
async function getPageData() {

  try {
    loading.value = true
    const res = await $getLargeHoldingsPageData({
      // time:time.value,
      pageNo: 1,
      pageSize: 50
    })
    if (res.success) {
      tableData.value = res.data.dataList
      total.value = res.data.total
      pagination.pageNo = res.data.current
      loading.value = false
    }
  } catch (error) {
    loading.value = false
    console.log(error);
  }


  // try {
  //     setTimeout(() => {
  //         tableData.value=[
  //             {
  //                 name:'张三',
  //                 age:18,
  //                 hobby:'打篮球'
  //             },
  //             {
  //                 name:'李四',
  //                 age:18,
  //                 hobby:'踢足球'
  //             },
  //             {
  //                 name:'王五',
  //                 age:18,
  //                 hobby:'游泳'
  //             },
  //         ]
  //     }, 2000);
  // } finally{
  //
  // }
}

// 查看走势
function viewHistory(val) {
  router.push({
    path: '/home/<USER>',
    query: {
      id: '3213213213'
    }
  })
}


function handleCurrentChange(val) {
  pagination.pageNo = val
  getPageData()
}

function handleSizeChange(val) {
  pagination.pageSize = val
  getPageData()
}

onMounted(() => {
  getPageData()

})


</script>

<style lang="scss" scoped>
.large-holdings-container {
  padding: 10px;
  padding-top: 20px;
  padding-bottom: 5px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .large-holdings-header {
    width: 100%;
    margin-bottom: 20px;
    background-color: #fff;

    .hold-form {
      display: flex;
      flex-wrap: wrap;
      position: relative;

      .form-item {
        width: 25%;

        :deep() {
          .el-input {
            width: 220px;
          }

          .el-select {
            width: 220px;
          }
        }
      }

      .reset-query {
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
  }

  .large-holdings-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;

    .large-holdings-table {
      width: 100%;
      flex: 1;
      overflow: auto;
    }

    .pagination {
      margin-top: 10px;
      display: flex;
      justify-content: flex-end;
      background-color: #fff;
    }
  }
}



</style>