<script setup>
import {onMounted, unref} from 'vue'
import {baseHost} from "../../utils/request.js"
import {useWebSocket} from '../../utils/webSocket.js'

const {isConnected, messages, error, sendMessage, progressObj} = useWebSocket(`ws://${baseHost}/v1/exchangeBot`)


const format = (val) => {
  const type = unref(progressObj)?.type
  return type ? type == 'HTTP_PROGRESS_BAR' ? '' : unref(progressObj)?.data?.status : '请先启动'
}
onMounted(() => {
})

</script>

<template>
  <main class="main">
    <div class="progress">
      <!-- 历史数据 -->
      <el-progress
          v-if="progressObj?.type=='HTTP_PROGRESS_BAR'"
          :text-inside="true"
          :stroke-width="20"
          :percentage="progressObj.data.c"
      >
        <span>{{ progressObj?.data?.c }}%</span>
      </el-progress>
      <!-- 实时数据 -->
      <el-progress
          v-else
          :percentage="80"
          :format="format"
          :indeterminate="true"
          :stroke-width="20"
      />
    </div>
    <div v-if="messages && messages.length > 0" class="content">
      <el-card v-for="(message, index) in messages" :key="index" class="box-card" style="margin-bottom: 20px">
        <div slot="header" class="clearfix">
          <span><strong>时间:</strong> {{ message.time }}</span>
        </div>
        <div>
          <el-col>
            <p><strong>触发策略:</strong> {{ message.data.klineStrategyEnum }}</p>
          </el-col>
          <el-col>
            <p><strong>当前余额:</strong> {{ message.data.currentBalance }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>操作金额:</strong> {{ message.data.opAmount }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>操作价格:</strong> {{ message.data.opPrice }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>操作类型:</strong> {{ message.data.opType }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>订单ID:</strong> {{ message.data.orderId }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>订单状态:</strong> {{ message.data.orderStatus }}</p>
          </el-col>
          <el-col :span="12">
            <p><strong>K线日期:</strong> {{ message.data.klineDate }}</p>
          </el-col>
          <el-row :gutter="20">
          </el-row>
        </div>
      </el-card>
    </div>
  </main>
</template>

<style lang="scss" scoped>
/* 样式 */
.box-card {
  margin-bottom: 20px;
}

.main {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  overflow: hidden;

  .progress {
    position: absolute;
    width: 100%;
    height: 40px;
    padding: 10px;
    top: 0;
    left: 0;

    .el-progress--line {
      margin-bottom: 15px;
      max-width: 600px;
    }
  }

  .content {
    flex: 1;
    margin-top: 40px;
    padding: 0 10px;
    overflow: auto;
    animation: scroll 1s steps(1) infinite;

    &:hover {
      animation-play-state: paused;
    }
  }
}

@keyframes scroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-400px);
  }
}
</style>
