export const currencyList = [
    {
        label: 'BTC',
        value: 'BTC'
    },
    {
        label: 'ETH',
        value: 'ETH'
    },
    {
        label: 'SOL',
        value: 'SOL'
    },
]

export const columns = [
    {prop: 'id', label: 'id',width: 140},
    {prop: 'symbol', label: '交易对',width: 100},
    {prop: 'interval', label: 'K线周期'},
    {prop: 'klineWindow', label: 'K线窗口大小',width: 110},
    {prop: 'takeProfit', label: '止盈百分点',width: 100},
    {prop: 'stopLossProfit', label: '止损百分点',width: 100},
    {prop: 'tradeMode', label: '交易模式',width: 110},
    {prop: 'klineStrategy', label: '交易策略',width: 110},
    {prop: 'accountInitialBalance', label: '账户初始余额',width: 110},
    {prop: 'position', label: '单次交易仓位',width: 110},
    {prop: 'endBalance', label: '最终余额',width: 110},
    {prop: 'yield', label: '最终收益率',width: 110},
    {prop: 'startTime', label: '策略开始时间',width: 180},
    {prop: 'endTime', label: '策略结束时间',width: 180},
    {prop: 'createTime', label: '创建时间',width: 180},
]