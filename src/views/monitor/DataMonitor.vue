<template>
  <div class="data-monitor-container">
    <!-- <div class="data-monitor-header">
        <el-form class="form"  label-width="80">
            <div class="form-item">
                <el-form-item label="日期：">
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        start-placeholder="Start Date"
                        end-placeholder="End Date"
                        :default-time="defaultTime"
                    />
                </el-form-item>
            </div>
            <div class="form-item operation">
                <el-button>清空</el-button>
                <el-button type="primary" @click="handleSearch">查询</el-button>
            </div>
        </el-form>
    </div> -->
    <div class="data-monitor-content">
      <div class="table-operation">
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
      <div class="data-monitor-table" v-if="!loading">
        <el-table :data="tableData" stripe border style="width: 100%;min-height: 400px;overflow: auto;" size="small">

          <el-table-column prop="id" label="ID" width="60"/>

          <el-table-column prop="projectMasterAddress" label="项目方地址" width="370">
            <template #default="{row}">
              <a :href="'https://solscan.io/account/'+row.projectMasterAddress" target="_blank"
                 style="color:#5f9ea0">{{ row.projectMasterAddress }}</a>
            </template>
          </el-table-column>

          <el-table-column prop="projectMasterSolBalance" label="项目方余额" width="120"/>

          <el-table-column prop="dstSymbol" label="token名字" width="110"/>

          <el-table-column prop="dstAddress" label="token地址" width="370">
            <template #default="{row}">
              <a :href="'https://avedex.cc/token/'+row.dstAddress+'-solana?from=Default'" target="_blank"
                 style="color:#ca4a5b">{{ row.dstAddress }}</a>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间"/>
          <!--                <el-table-column prop="updateTime" label="修改时间" />-->
          <template #empty>
            <div class="empty">
              暂无数据
            </div>
          </template>
        </el-table>
      </div>
      <div class="mask" v-else>
        <svg class="circular" viewBox="0 0 50 50">
          <circle class="path" cx="25" cy="25" r="20" fill="none"/>
        </svg>
      </div>
      <div class="pagination">
        <el-pagination
            v-model:current-page="pagination.pageNo"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[20, 30, 40, 50]"
            :small="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>


</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
import {$export, $getMonitorData} from '../../api/dataMonitor'
import {baseURL} from "../../utils/request.js";

const defaultTime = new Date(2000, 1, 1, 12, 0, 0) // '12:00:00'
const time = ref('')
const queryForm = reactive({})
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const pagination = reactive({
  pageNo: 1,
  pageSize: 20
})

function handleSearch() {
  getMonitorData()
}

async function exportExcel() {
  const a = document.createElement('a')
  a.href = baseURL + '/sol/export'
  document.body.appendChild(a)
  a.click()

  // let res = await $export();
  // console.log(res)
  // const blob = new Blob([res], {
  //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  // })
  // const a = document.createElement('a')
  // const href = window.URL.createObjectURL(blob)
  // console.log(href)
  //
  // a.download = '11'
  // document.body.appendChild(a)
  // a.click()
  // console.log(a)
  // document.body.removeChild(a)
  // window.URL.revokeObjectURL(href)

  // ElMessage({
  //   message: '还没开发，么慌~',
  //   type: 'warning',
  // })

}

async function getMonitorData() {
  try {
    loading.value = true
    const res = await $getMonitorData({
      // time:time.value,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize
    })
    if (res.success) {
      tableData.value = res.data.dstPoList
      total.value = res.data.total
      pagination.pageNo = res.data.current
      loading.value = false
    }
  } catch (error) {
    loading.value = false
    console.log(error);
  }
}

function handleCurrentChange(val) {
  pagination.pageNo = val
  getMonitorData()
}

function handleSizeChange(val) {
  pagination.pageSize = val
  getMonitorData()
}

onMounted(() => {
  getMonitorData()
})

</script>

<style lang="scss" scoped>
.empty {
  width: 100%;
  height: 300px;
  line-height: 300px;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
}

// 遮罩层
.mask {
  z-index: 9999;
  position: absolute;
  width: 100%;
  height: calc(100vh - 80px);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  background-color: rgba(255, 255, 255, 0.7);
}

.circular {
  display: inline;
  height: 30px;
  width: 30px;
  animation: loading-rotate 2s linear infinite;
}

.path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: #409eff;
  stroke-linecap: round;
}

@keyframes loading-rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}

.data-monitor-container {
  padding: 10px;
  padding-top: 20px;
  padding-bottom: 5px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .data-monitor-header {
    width: 100%;
    margin-bottom: 20px;
    background-color: #fff;

    .form {
      display: flex;
      flex-wrap: wrap;
      position: relative;

      .form-item {
        width: 25%;
        height: 32px;
      }

      .operation {
        position: absolute;
        bottom: 0;
        right: 0;
        display: flex;
        justify-content: end;
        padding-right: 20px;
        // align-self: flex-end;
      }
    }
  }

  .data-monitor-content {
    flex: 1;
    // background-color: pink;
    position: relative;

    .table-operation {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
    }

    .data-monitor-table {
      width: 100%;
      min-height: 300px;
      max-height: 500px;
      overflow: auto;
    }

    .pagination {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 80px;
      padding-right: 10px;
      display: flex;
      justify-content: flex-end;
      background-color: #fff;
      z-index: 9;
    }
  }
}
</style>