<template>
  <div class="config-container">
    <!-- 表单 -->
    <div class="form">
      <el-form :model="form" label-width="120" style="max-width: 800px">
        <el-form-item label="当前监控地址：">
          <div class="range-content">
            <el-input class="input" v-model="form.address" style="width: 460px"/>
          </div>
        </el-form-item>
        <el-form-item label="黑名单：">
          <el-input v-model="blackList" style="width: 460px" :autosize="{ minRows: 1}" type="textarea"
                    placeholder="请输入"/>
        </el-form-item>
      </el-form>
    </div>
    <el-button type="primary" class="save" @click="handleSave" v-text="loading?'提交中':'保存'"
               :disabled="loading"></el-button>
  </div>
</template>

<script setup>
import {ref, onMounted, reactive} from 'vue'
import {$getConfig, $updateConfig, $getBlackList, $updateBlackList} from '../../api/largeHolding'
import {ElMessage} from 'element-plus'

const loading = ref(false)
const form = reactive({
  // min: 0,
  // max: 0,
  address: 0
})
const blackList = ref('')

// 获取参数
async function getConfig() {
  try {
    const res = await $getConfig()
    if (res.success) {
      form.address = res.data.address
    }
  } catch (error) {
    console.log(error);
  }
}

// 修改参数
async function updateConfig() {
  console.log(form)
  try {
    loading.value = true
    const res = await $updateConfig({
      // min: form.min,
      // max: form.max,
      address: form.address
    })
    if (res.success) {
      ElMessage({
        message: '提交成功~',
        type: 'success',
      })
      loading.value = false
      getConfig()
    }
  } catch (error) {
    ElMessage({
      message: error,
      type: 'error',
    })
    loading.value = false
  }
}

// 获取黑名单
async function getBlackList() {
  try {
    const res = await $getBlackList()
    if (res.success) {
      blackList.value = res.data.address
    }
  } catch (error) {
    console.log(error);
  }
}

// 修改黑名单
async function updateBlackList() {
  try {
    const res = await $updateBlackList({
      blackAddress: blackList.value
    })
  } catch (error) {

  }
}

// 点击保存
function handleSave() {
  updateConfig()
  updateBlackList()
}


onMounted(() => {
  getConfig()
  getBlackList()
})

</script>

<style lang="scss" scoped>
.input {
  width: 220px;
}

.config-container {
  width: 100%;
  height: 100%;
  padding: 10px;
  position: relative;

  .save {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 80px;
  }
}
</style>