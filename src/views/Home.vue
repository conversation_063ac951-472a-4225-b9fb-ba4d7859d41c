<template>
  <div class="home-container">
    <div class="home-left">
      <div class="theme">
        blate韭菜系统
      </div>
      <el-menu
          :default-active="route.path"
          class="el-menu-vertical-demo"
          @open="handleOpen"
          @close="handleClose"
          background-color="#545c64"
          text-color="#fff"
          active-color="yellow"
          router
          style="border: none;"
      >       
<!--        <el-sub-menu index="2">-->

<!--          <template #title>-->
<!--            <el-icon>-->
<!--              <House/>-->
<!--            </el-icon>-->
<!--            <span>大户持仓监控</span>-->
<!--          </template>-->

<!--          <el-menu-item index="/home/<USER>">-->
<!--            <el-icon>-->
<!--              <Tickets/>-->
<!--            </el-icon>-->
<!--            <template #title>持仓列表</template>-->
<!--          </el-menu-item>-->

<!--          <el-menu-item index="/home/<USER>">-->
<!--            <el-icon>-->
<!--              <setting/>-->
<!--            </el-icon>-->
<!--            <template #title>持仓列表配置</template>-->
<!--          </el-menu-item>-->

<!--        </el-sub-menu>-->


<el-sub-menu v-for="(subMenu,i) in menuList" :index="i+1" :key="i">
  <template #title>
    <CustomIcon :icon-name="subMenu.meta.icon"></CustomIcon>
    <span>{{ subMenu.meta.title }}</span>
  </template>
  <el-menu-item v-for="(menuItem,j) in subMenu.children" :index="menuItem.path" :key="j">
    <CustomIcon :icon-name="menuItem.meta.icon"></CustomIcon>
    <template #title>{{ menuItem.meta.title }}</template>
  </el-menu-item>
</el-sub-menu>

      </el-menu>
    </div>
    <div class="home-right">
      <div class="home-header">
        {{ title }}
      </div>
      <div class="home-content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, computed, onMounted, watch} from 'vue'
import {useRouter, useRoute} from 'vue-router';
import {menuList} from '../router/config'
import CustomIcon from '../components/widget/customIcon.vue';



const router = useRouter()
const route = useRoute()
const title = ref('数据监控')

function handleClose() {}
function handleOpen() {}




watch(() => route, (n, o) => {
  title.value = n.meta.title
}, {immediate: true, deep: true})

onMounted(()=>{})

</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// .el-menu-vertical-demo:not(.el-menu--collapse) {
//   width: 200px;
//   min-height: 400px;
// }

.home-container {
  width: 100vw;
  height: 100vh;
  display: flex;

  .home-left {
    z-index: 999;
    background-color: #545c64;
    width: 200px;

    .theme {
      width: 100%;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      color: #fff;
    }
  }

  .home-right {
    flex: 1;
    display: flex;
    flex-direction: column;

    .home-header {
      width: 100%;
      height: 80px;
      border-bottom: 1px solid #999;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
    }

    .home-content {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>