import {createRouter, createWebHistory} from "vue-router";


export const constantRoutes = [
    {
        path: '/',
        redirect: '/home'
    },
    {
        path: '/home',
        name: 'home',
        meta: {
            icon: 'User',
            title: '交易所策略机器人'
        },
        component: () => import('../views/Home.vue'),
        children: [
            {
                path: '/home',
                redirect: '/home/<USER>'
            },
            // //数据监控
            // {
            //     path: '/home/<USER>',
            //     name: 'DataMonitor',
            //     meta: {
            //         title: '数据监控'
            //     },
            //     component: () => import('../views/monitor/DataMonitor.vue')
            // },
            // 参数配置
            {
                path: '/home/<USER>',
                name: 'ParamsConfig',
                meta: {
                    icon: 'Setting',
                    title: '启动配置'
                },
                component: () => import('../views/exchangesBot/StartConfig.vue')
            },
            // {
            //     path: '/home/<USER>',
            //     name: 'DataPanel',
            //     meta: {
            //         icon: 'Monitor',
            //         title: '实时面板'
            //     },
            //     component: () => import('../views/exchangesBot/RealPanel.vue')
            // },
            {
                path: '/home/<USER>',
                name: 'LargeHoldingsList',
                meta: {
                    icon: 'Histogram',
                    title: '策略排行榜'
                },
                component: () => import('../views/exchangesBot/StrategyList.vue')
            },
            // // 大户持仓详情
            // {
            //     path: '/home/<USER>',
            //     name: 'LargeHoldingsDetail',
            //     meta: {
            //         title: '历史走势'
            //     },
            //     component: () => import('../views/largeHoldings/detail.vue')
            // },
            // // 大户持仓列表
            // {
            //     path: '/home/<USER>',
            //     name: 'largeHoldingsConfig',
            //     meta: {
            //         title: '大户持仓配置'
            //     },
            //     component: () => import('../views/largeHoldings/StartConfig.vue')
            // },
        ]
    }
]


const router = createRouter({
    history: createWebHistory(),
    routes: constantRoutes
})

export default router

