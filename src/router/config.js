import {constantRoutes} from './index'


/**
 * 
 * @param {*} routes 
 * @returns 返回过滤没有name属性的真实渲染路由数组
 */
function filterRoutes(routes){
    let newRoutes=[]
    for(let i=0;i<routes.length;i++){
        let route=routes[i]
        if('name' in route){
            if('children' in route){
                route.children=route.children.filter(child=>'name' in child)
            }
            newRoutes.push(route)
        }
    }
    // console.log(newRoutes);
    return newRoutes
}

export const menuList=filterRoutes(constantRoutes)