<template>
  <div>
    <el-icon>
        <component :is="icons[iconName]"></component>
    </el-icon>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 接收父组件传递的图标参数
const props=defineProps({
    iconName:{
        type:String,
        required:true
    }
})

// 创建一个响应式对象来存储图标组件
const icons=ref(Object.fromEntries(Object.entries(ElementPlusIconsVue).map(([key,component])=>[key,component])))

</script>

<style>

</style>