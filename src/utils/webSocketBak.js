import { ref, onMounted, onBeforeUnmount } from 'vue';

// WebSocket 状态管理
export function webSocket(url) {
  const socket = ref(null); // WebSocket 实例
  const messages = ref([]); // 存储接收到的消息
  const isConnected = ref(false); // 连接状态
  const progressObj=ref(null) //进度条对象

  // 连接错误信息
  const error = ref('');

  // 创建 WebSocket 连接
  const connect = () => {
    socket.value = new WebSocket(url);

    // 连接成功时
    socket.value.onopen = () => {
      isConnected.value = true;
      console.log('Connected to WebSocket server');
    };

    // 接收到服务器的消息
    socket.value.onmessage = (event) => {
      try {
        // console.log('event',event);
        const message = JSON.parse(event.data);
        const maxLength = 10000000; // 最大存储条目数
        // console.log('Received message:', message);
        if (messages.value.length >= maxLength) {
          messages.value.shift();
        }
        if(message.tag=='POSITION'){
          messages.value.push(message);
        }else{
          progressObj.value={
            type:message.tag,
            data:message.data
          }
        }
       
        // console.log(`Received message: ${message}, size : ${messages.value.length}`);
      } catch (err) {
        error.value = 'Error parsing message';
        console.error('Error parsing message:', err);
      }
    };

    // 连接关闭时
    socket.value.onclose = () => {
      isConnected.value = false;
      console.log('Disconnected from WebSocket server');
    };

    // 连接发生错误时
    socket.value.onerror = (err) => {
      error.value = 'WebSocket error occurred';
      console.error('WebSocket error:', err);
    };
  };

  // 发送消息
  const sendMessage = (message) => {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      socket.value.send(JSON.stringify(message));
      console.log('Sent message:', message);
    } else {
      error.value = 'WebSocket is not open';
      console.error('WebSocket is not open');
    }
  };

  // 组件卸载时关闭 WebSocket 连接
  // onBeforeUnmount(() => {
  //   if (socket.value) {
  //     socket.value.close();
  //     console.log('WebSocket connection closed');
  //   }
  // });

  // 在组件销毁时关闭 WebSocket 连接
  onBeforeUnmount(() => {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
      console.log('Closing WebSocket connection...');
      socket.value.close();
    }
  });

  // 自动重连机制
  const reconnect = () => {
    if (!isConnected.value) {
      console.log('Attempting to reconnect...');
      connect();
    }
  };

  // 定时重连
  setInterval(reconnect, 5000); // 每 5 秒尝试重连

  // 初始化连接
  onMounted(() => {
    connect();
  });

  return {
    socket,
    isConnected,
    messages,
    progressObj,
    error,
    sendMessage,
  };
}
