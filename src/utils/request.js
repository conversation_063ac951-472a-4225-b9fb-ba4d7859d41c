import axios from 'axios'

// export const baseHost = 'localhost:9080'
// export const baseHost = '*************:9080'
export const baseHost = '************:9080'
// export const baseHost = '**************:9080'

const baseURL = `http://${baseHost}`

const instance = axios.create({
    baseURL: baseURL,
    timeout: 1000 * 60,
    headers: {
        'Content-Type': 'application/json;charset=utf-8'
    }
})

// request拦截器x
instance.interceptors.request.use(
    (config) => {
        // if(localStorage.getItem('token')){
        //     console.log('有token:',localStorage.getItem('token'));
        //     config.headers['gksk-access-token']=localStorage.getItem('token')
        //     config.headers['gksk-tenant-code']='sksy-paas'
        //     config.headers['gksk-tenant-id']='509440261685253'
        // }
        return config
    }
)

//response拦截器
instance.interceptors.response.use(
    (response) => {
        if (response.status === 200) {
            if (response.data.success) {
                return response.data;
            } else {
                return Promise.reject(response.data.message);
            }
        } else {
            return Promise.reject(new Error(`请求失败，状态码：${response.status}`));
        }
    },
    (error) => {
        return Promise.reject('服务器跑路了，待会再试'); // 抛出错误
    }
);

export default instance