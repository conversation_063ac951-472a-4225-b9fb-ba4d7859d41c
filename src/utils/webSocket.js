import { ref, onMounted, onBeforeUnmount } from 'vue';

const globalSocket = ref(null);//全局 WebSocket 实例

// WebSocket 状态管理
export function useWebSocket(url) {
  const messages = ref([]); // 存储接收到的消息
  const isConnected = ref(false); // 连接状态
  const progressObj=ref(null) //进度条对象

  // 连接错误信息
  const error = ref('');

  // 创建 WebSocket 连接
  const connect = () => {
    if (globalSocket.value) {
      // console.log('WebSocket 已存在，复用现有连接');
      return;
    }
    globalSocket.value = new WebSocket(url);
    globalSocket.value.onopen = () => {
      isConnected.value = true;
      console.log('Connected to WebSocket server');
    };
    // 接收到服务器的消息
    globalSocket.value.onmessage = (event) => {
      try {
        // console.log('event',event);
        const message = JSON.parse(event.data);
        const maxLength = 10000000; // 最大存储条目数
        // console.log('Received message:', message);
        if (messages.value.length >= maxLength) {
          messages.value.shift();
        }
        if(message.tag=='POSITION'){
          messages.value.push(message);
        }else{
          progressObj.value={
            type:message.tag,
            data:message.data
          }
        }

        // console.log(`Received message: ${message}, size : ${messages.value.length}`);
      } catch (err) {
        error.value = 'Error parsing message';
        console.error('Error parsing message:', err);
      }
    };

    // 连接关闭时
    globalSocket.value.onclose = () => {
      isConnected.value = false;
      console.log('Disconnected from WebSocket server');
    };

    // 连接发生错误时
    globalSocket.value.onerror = (err) => {
      error.value = 'WebSocket error occurred';
      console.error('WebSocket error:', err);
    };
  };

  // 发送消息
  const sendMessage = (message) => {
    if (globalSocket.value && globalSocket.value.readyState === WebSocket.OPEN) {
      globalSocket.value.send(JSON.stringify(message));
      console.log('Sent message:', message);
    } else {
      error.value = 'WebSocket is not open';
      console.error('WebSocket is not open');
    }
  };

  // 在组件销毁时关闭 WebSocket 连接
  onBeforeUnmount(() => {
    if (globalSocket.value && globalSocket.value.readyState === WebSocket.OPEN) {
      console.log('Closing WebSocket connection...');
      globalSocket.value.close();
    }
  });

  // 自动重连机制
  const reconnect = () => {
    if (!isConnected.value) {
      console.log('Attempting to reconnect...');
      connect();
    }
  };

  // 定时重连
  setInterval(reconnect, 5000); // 每 5 秒尝试重连

  // 初始化连接
  onMounted(() => {
    connect();
  });

  return {
    isConnected,
    messages,
    progressObj,
    error,
    sendMessage,
    connect
  };
}
