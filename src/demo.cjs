const puppeteer = require("puppeteer");
const axios = require("axios");

(async () => {
    const browser = await puppeteer.launch({
        headless: false,  // 需要打开可见的 Chrome 窗口
        args: ["--remote-debugging-port=9222"] // 启用远程调试
    });

    const page = await browser.newPage();
    await page.goto("https://gmgn.ai/new-pair/LtR3Zvav?chain=bsc");
    await page.locator('.chakra-portal>div>div>svg').click();

    const client = await page.target().createCDPSession();
    await client.send("Network.enable");

    let num = 1
    client.on("Network.webSocketFrameReceived", async (event) => {
        try {
            const data = JSON.parse(event.response.payloadData);
            if (data.channel === "new_pool_info" && Array.isArray(data.data)) {
                for (const pool of data.data) {
                    if (Array.isArray(pool.p)) {
                        for (const entry of pool.p) {
                            const id = entry.id;
                            const a = entry.a;
                            const ot = entry.ot;//时间
                            //结尾不是4444的不管
                            if (!a.endsWith("4444")) {
                                continue;
                            }
                            const s = entry.bti?.s || "N/A";
                            const n = entry.bti?.n || "N/A";
                            const timestamp = new Date().toLocaleString("zh-CN", {timeZone: "Asia/Shanghai"});
                            console.log(`(${num++}) ${timestamp} id: ${id} ot: ${ot}, name1: ${s}, name2: ${n}, 合约: ${a}`);
                            const message = `${timestamp}\nname1:\`${s}\`\nname2:\`${n}\`\n合约:\`${a}\``;
                            await sendMessage(id, ot, s, n, a);
                            console.log("------------------------------------");
                        }
                    }
                }
            }
        } catch (error) {
            console.error("解析 WebSocket 消息出错:", error);
        }
    });


    client.on("Network.webSocketFrameSent", (event) => {
        console.log("发送 WebSocket 消息:", event.response.payloadData);
    });

    client.on("Network.webSocketCreated", (event) => {
        console.log("WebSocket 连接创建:", event);
    });

})();

// 发送 HTTP 请求的函数
async function sendMessage(id, ot, name1, name2, address) {
    try {
        const response = await axios.post("http://localhost:9889/sendBscMessage", {
            // const response = await axios.post("http://************:9889/sendBscMessage", {
            // const response = await axios.post("http://*************:9889/sendBscMessage", {

            chatId: -4184019940,//正式群
            // chatId: -4270235424,//测试群
            bscContract: {
                id: id,
                ot: ot,
                name1: name1,
                name2: name2,
                address: address
            }
        });
        // console.log("消息发送成功:", response.data);
    } catch (error) {
        console.error("消息发送失败:", error.message);
    }
}
