const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const axios = require('axios');

const {envConfig} = require('./config/env.cjs');

const chatId = envConfig.get('chatId');
const tgBotUrl = envConfig.get('tgBotUrl');

puppeteer.use(StealthPlugin());


(async () => {
    const browser = await puppeteer.launch({
        headless: false, // 非 headless 模式防止网站屏蔽
        // executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome', // macOS 本地 Chrome 路径
        defaultViewport: null,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-blink-features=AutomationControlled',
            '--start-maximized'
        ]
    });

    const page = await browser.newPage()


    // 创建 CDP Session
    const client = await page.target().createCDPSession();
    await client.send('Network.enable');

    // 设置真实用户 UA
    await page.setUserAgent(
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
    );

    // 监听特定API请求的请求头
    const targetApiUrl = 'https://www.oklink.com/api/explorer/v2/index/text-show';
    let requestCount = 0;

    client.on('Network.requestWillBeSent', (event) => {
        // 检查是否是目标API请求
        if (event.request.url.startsWith(targetApiUrl)) {
            requestCount++;
            const timestamp = new Date().toLocaleString("zh-CN", {timeZone: "Asia/Shanghai"});
            console.log(`🎯 第${requestCount}次捕获到目标API请求 [${timestamp}]:`);
            console.log('URL:', event.request.url);
            console.log('Method:', event.request.method);
            console.log('请求头:', JSON.stringify(event.request.headers, null, 2));
            console.log('====================================');
        }
    });

    // 监听 WebSocket 消息
    let num = 1;
    client.on("Network.webSocketFrameReceived", async (event) => {
        try {
            const data = JSON.parse(event.response.payloadData);
            // console.log("接收 WebSocket 响应:", data);
            if (data.channel === "new_pool_info" && Array.isArray(data.data)) {
                for (const pool of data.data) {
                    if (Array.isArray(pool.p)) {
                        for (const entry of pool.p) {
                            const id = entry.id;
                            const a = entry.a;
                            const ot = entry.ot;//时间
                            if (!a.endsWith("4444")) continue;
                            const s = entry.bti?.s || "N/A";
                            const n = entry.bti?.n || "N/A";
                            const timestamp = new Date().toLocaleString("zh-CN", {timeZone: "Asia/Shanghai"});
                            console.log(`(${num++}) ${timestamp} id: ${id} ot: ${ot}, name1: ${s}, name2: ${n}, 合约: ${a}`);
                            const contractData = {
                                id: id,
                                ot: ot,
                                name1: s,
                                name2: n,
                                address: a
                            };
                            await sendMessage(contractData, tgBotUrl, chatId);
                            console.log("------------------------------------");
                        }
                    }
                }
            }
        } catch (error) {
            console.error("解析 WebSocket 消息出错:", error);
        }
    });

    const targetURL = 'https://www.oklink.com/zh-hans';
    console.log('🚀 正在打开:', targetURL);

    await page.goto(targetURL, {
        waitUntil: 'networkidle2',
        timeout: 60000
    });

    console.log('🎯 页面加载完毕，开始每1秒刷新页面以获取API请求头');

    // 每隔1秒刷新页面
    let refreshCount = 0;
    const refreshInterval = setInterval(async () => {
        try {
            refreshCount++;
            console.log(`\n🔄 第${refreshCount}次刷新页面...`);
            await page.reload({
                waitUntil: 'domcontentloaded',  // 更快的刷新
                timeout: 15000
            });
            // 短暂等待让API请求有时间发出
            await sleep(500);
            console.log(`✅ 第${refreshCount}次刷新完成\n`);
        } catch (error) {
            console.error(`❌ 第${refreshCount}次刷新失败:`, error.message);
        }
    }, 1000); // 每1秒刷新一次

    // 可以通过注释掉下面这行来让程序持续运行
    // clearInterval(refreshInterval);

    // 不关闭浏览器，方便调试
    // await browser.close();
})();

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 发送 HTTP 请求的函数
async function sendMessage(contractData, url, chatId) {
    try {
        const response = await axios.post(url, {
            chatId: chatId,
            bscContract: contractData
        });
        // console.log("消息发送成功:", response.data);
    } catch (error) {
        console.error("消息发送失败:", error.message);
    }
}
