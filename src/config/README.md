# 环境配置模块使用说明

## 概述

`env.cjs` 是一个环境变量管理模块，用于统一管理项目中的环境配置。它会根据 `NODE_ENV` 自动加载对应的环境文件。

## 功能特性

- ✅ 自动根据 `NODE_ENV` 加载对应的 `.env` 文件
- ✅ 提供单例模式，确保环境变量只加载一次
- ✅ 支持默认值设置
- ✅ 提供详细的环境信息输出
- ✅ 错误处理和警告提示

## 环境文件规则

- `NODE_ENV=dev` → 加载 `.env.dev`
- `NODE_ENV=test` → 加载 `.env.test`
- `NODE_ENV=prod` → 加载 `.env.prod`
- 未设置 `NODE_ENV` → 默认加载 `.env.dev`

## 使用方法

### 1. 基本用法

```javascript
// 引入环境配置模块
const { get, printEnvInfo, getCommonVars } = require('./config/env.cjs');

// 获取单个环境变量
const chatId = get('chatId');
const apiUrl = get('API_URL', 'http://localhost:3000'); // 带默认值

// 获取常用环境变量
const { chatId, tgBotUrl, nodeEnv } = getCommonVars();

// 打印环境信息
printEnvInfo();
```

### 2. 高级用法

```javascript
// 引入完整的配置对象
const { envConfig } = require('./config/env.cjs');

// 获取环境信息
const envInfo = envConfig.getEnvInfo();
console.log('环境信息:', envInfo);

// 检查是否加载成功
if (envConfig.isLoaded) {
    console.log('环境配置加载成功');
}
```

## API 参考

### 方法

#### `get(key, defaultValue)`
获取环境变量值
- `key`: 环境变量名
- `defaultValue`: 默认值（可选）
- 返回: 环境变量值或默认值

#### `getCommonVars()`
获取常用环境变量
- 返回: `{ chatId, tgBotUrl, nodeEnv }`

#### `getEnvInfo()`
获取环境信息
- 返回: `{ nodeEnv, envFile, envFilePath, isLoaded }`

#### `printEnvInfo()`
打印格式化的环境信息

### 属性

#### `envConfig.isLoaded`
环境文件是否加载成功

#### `envConfig.envFile`
当前使用的环境文件名

#### `envConfig.envFilePath`
环境文件的完整路径

## 示例

查看 `src/example-usage.cjs` 文件了解完整的使用示例。

## 注意事项

1. 环境文件应放在项目根目录
2. 环境变量名区分大小写
3. 建议在应用启动时就调用环境配置模块
4. 如果环境文件不存在，会显示警告但不会中断程序
