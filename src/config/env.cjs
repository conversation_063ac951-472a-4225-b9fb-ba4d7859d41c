const dotenv = require('dotenv');
const path = require('path');

/**
 * 环境配置加载器
 * 根据 NODE_ENV 自动加载对应的环境文件
 */
class EnvConfig {
    constructor() {
        this.envFile = null;
        this.envFilePath = null;
        this.isLoaded = false;
        this.loadEnv();
    }

    /**
     * 加载环境变量
     */
    loadEnv() {
        // 根据环境变量选择对应的 .env 文件
        this.envFile = process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env.dev'; // 默认使用开发环境

        // 构建环境文件的完整路径 (从项目根目录)
        this.envFilePath = path.resolve(__dirname, '../..', this.envFile);

        // 加载环境文件
        const result = dotenv.config({ path: this.envFilePath });

        if (result.error) {
            console.warn(`⚠️  环境文件加载失败: ${this.envFilePath}`);
            console.warn(`错误信息: ${result.error.message}`);
        } else {
            this.isLoaded = true;
            console.log(`✅ 环境文件加载成功: ${this.envFile}`);
        }
    }

    /**
     * 获取环境变量值
     * @param {string} key - 环境变量名
     * @param {string} defaultValue - 默认值
     * @returns {string} 环境变量值
     */
    get(key, defaultValue = undefined) {
        const value = process.env[key];
        if (value === undefined && defaultValue !== undefined) {
            return defaultValue;
        }
        return value;
    }

    /**
     * 获取所有环境信息
     * @returns {object} 环境信息对象
     */
    getEnvInfo() {
        return {
            nodeEnv: process.env.NODE_ENV,
            envFile: this.envFile,
            envFilePath: this.envFilePath,
            isLoaded: this.isLoaded
        };
    }

    /**
     * 打印环境信息
     */
    printEnvInfo() {
        const info = this.getEnvInfo();
        console.log('=== 环境配置信息 ===');
        console.log(`当前读取的环境文件是: ${info.envFile}`);
        console.log(`环境文件完整路径: ${info.envFilePath}`);
        console.log(`当前 NODE_ENV: ${info.nodeEnv}`);
        console.log(`加载状态: ${info.isLoaded ? '成功' : '失败'}`);
        console.log('==================');
    }

}

// 创建单例实例
const envConfig = new EnvConfig();

module.exports = {
    envConfig
};

