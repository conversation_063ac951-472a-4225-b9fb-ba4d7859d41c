<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <link rel="icon" type="image/svg+xml" href="/wzfj.svg"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>blate韭菜系统</title>
    <style>
        .waterWrapper {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-wrap: wrap;
            pointer-events: none;
            overflow: hidden;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
<div id="app"></div>
<script type="module" src="/src/main.js"></script>
</body>
<script>

    // div和绝对定位形式
    function cssHelper(el, prototype) {
        for (let i in prototype) {
            el.style[i] = prototype[i]
        }
    }

    function createItem() {
        const item = document.createElement('div')
        item.innerHTML = 'blate'
        cssHelper(item, {
            position: 'absolute',
            top: `50px`,
            left: `50px`,
            fontSize: `16px`,
            color: '#000',
            lineHeight: 1.5,
            opacity: 0.1,
            transform: `rotate(-15deg)`,
            transformOrigin: '0 0',
            userSelect: 'none', // 用户无法选中
            whiteSpace: 'nowrap',
        })
        return item;
    }

    function createWater() {
        const waterHeight = 100;
        const waterWidth = 160;
        const {clientWidth, clientHeight} = document.documentElement ||
        document.body;
        // 不能使用ceil向上取整，否则会出现超出一屏的水印
        const column = Math.floor(clientWidth / waterWidth);
        const rows = Math.floor(clientHeight / waterHeight);
        const waterWrapper = document.createElement('div');
        waterWrapper.className = 'waterWrapper'
        for (let i = 0; i < column * rows; i++) {
            const wrap = document.createElement('div');
            cssHelper(wrap, Object.create({
                position: 'relative',
                width: `${waterWidth}px`,
                height: `${waterHeight}px`,
                flex: `0 0 ${waterWidth}px`,
                overflow: 'hidden',
            }));
            wrap.appendChild(createItem());
            waterWrapper.appendChild(wrap)
        }
        document.body.appendChild(waterWrapper)
    }

    createWater();

    window.onresize = function () {
        const wrapper =
            document.getElementsByClassName('waterWrapper')[0];
        document.body.removeChild(wrapper);
        createWater();
    }


</script>
</html>
