import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 8080,
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcssor: 'scss',
      patterns: []
    }
  },
  // devServer: {
  //     proxy: {
  //         '/sdk': {
  //             target: 'http://10.31.26.44:8080',
  //             ws: true,
  //             changeOrigin: true,
  //         }
  //     }
  // },
})
